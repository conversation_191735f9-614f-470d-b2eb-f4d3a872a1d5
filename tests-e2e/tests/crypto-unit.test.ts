import { test, expect } from "@playwright/test"

test.describe("密钥加密功能单元测试", () => {
  
  test("密钥生成和哈希验证", async ({ request }) => {
    // 测试创建应用时的密钥生成
    const createResponse = await request.post("http://localhost:2022/trpc/application.create", {
      data: {
        name: "测试应用",
        description: "测试密钥加密"
      },
      headers: {
        "Content-Type": "application/json",
        // 这里需要添加认证 token，实际应用中需要先登录获取
      }
    })

    console.log("创建应用响应状态:", createResponse.status())
    
    if (createResponse.status() === 200) {
      const responseData = await createResponse.json()
      console.log("创建应用响应:", responseData)
      
      // 验证响应包含密钥信息
      expect(responseData.result?.secret).toBeDefined()
      expect(responseData.result?.secret).toMatch(/^sk_/)
      expect(responseData.result?.secretDisplay).toBeDefined()
    }
  })

  test("开放平台认证 API 测试", async ({ request }) => {
    // 测试无效 appId 的情况
    const invalidAppIdResponse = await request.post("http://localhost:2022/trpc/openapi.authenticate", {
      data: {
        appId: "invalid_app_id",
        secret: "any_secret"
      },
      headers: {
        "Content-Type": "application/json"
      }
    })

    console.log("无效 appId 响应状态:", invalidAppIdResponse.status())
    
    // 由于是无效请求，应该返回 400 或 401
    expect([400, 401, 500]).toContain(invalidAppIdResponse.status())

    // 测试空参数的情况
    const emptyParamsResponse = await request.post("http://localhost:2022/trpc/openapi.authenticate", {
      data: {},
      headers: {
        "Content-Type": "application/json"
      }
    })

    console.log("空参数响应状态:", emptyParamsResponse.status())
    expect([400, 401, 500]).toContain(emptyParamsResponse.status())
  })

  test("服务器健康检查", async ({ request }) => {
    // 首先测试服务器是否可达
    const healthResponse = await request.get("http://localhost:2022/health")
    
    console.log("健康检查响应状态:", healthResponse.status())
    
    if (healthResponse.status() === 200) {
      const healthData = await healthResponse.json()
      console.log("健康检查响应:", healthData)
      expect(healthData).toHaveProperty("message")
    } else {
      console.log("服务器可能未启动，状态码:", healthResponse.status())
    }
  })

  test("密钥格式验证", async () => {
    // 测试密钥格式的正则表达式
    const validSecrets = [
      "sk_abcdef1234567890",
      "sk_123456789012345678901234567890ab",
      "sk_ABCDEF1234567890abcdef1234567890"
    ]

    const invalidSecrets = [
      "invalid_secret",
      "sk_",
      "sk_short",
      "not_sk_prefixed",
      ""
    ]

    validSecrets.forEach(secret => {
      expect(secret).toMatch(/^sk_[a-zA-Z0-9]{16,}$/)
    })

    invalidSecrets.forEach(secret => {
      expect(secret).not.toMatch(/^sk_[a-zA-Z0-9]{16,}$/)
    })
  })

  test("API 路径验证", async ({ request }) => {
    // 测试 tRPC 路径是否正确
    const paths = [
      "/trpc/application.create",
      "/trpc/application.regenerateSecret", 
      "/trpc/openapi.authenticate",
      "/trpc/openapi.verify"
    ]

    for (const path of paths) {
      try {
        const response = await request.post(`http://localhost:2022${path}`, {
          data: {},
          headers: {
            "Content-Type": "application/json"
          }
        })
        
        console.log(`路径 ${path} 响应状态:`, response.status())
        
        // 不期望 404，说明路径存在（即使返回其他错误）
        expect(response.status()).not.toBe(404)
      } catch (error) {
        console.log(`路径 ${path} 测试失败:`, error)
      }
    }
  })
})

test.describe("性能和负载测试", () => {
  test("并发密钥验证性能", async ({ request }) => {
    const startTime = Date.now()
    
    // 并发发送多个认证请求
    const promises = Array.from({ length: 10 }, (_, i) => 
      request.post("http://localhost:2022/trpc/openapi.authenticate", {
        data: {
          appId: `test_app_${i}`,
          secret: `sk_testsecret${i.toString().padStart(8, '0')}`
        },
        headers: {
          "Content-Type": "application/json"
        }
      })
    )

    const responses = await Promise.all(promises)
    const endTime = Date.now()
    
    console.log(`10个并发请求耗时: ${endTime - startTime}ms`)
    
    // 验证所有请求都得到了响应（不管是成功还是失败）
    responses.forEach((response, index) => {
      console.log(`请求 ${index} 状态:`, response.status())
      expect(response.status()).toBeGreaterThanOrEqual(200)
      expect(response.status()).toBeLessThan(600)
    })
    
    // 验证并发处理时间在合理范围内（比如2秒以内）
    expect(endTime - startTime).toBeLessThan(2000)
  })

  test("内存泄漏检测（简单版）", async ({ request }) => {
    // 发送大量请求来检测可能的内存泄漏
    const iterations = 50
    
    for (let i = 0; i < iterations; i++) {
      try {
        await request.post("http://localhost:2022/trpc/openapi.authenticate", {
          data: {
            appId: `stress_test_${i}`,
            secret: `sk_stress${i.toString().padStart(10, '0')}`
          },
          headers: {
            "Content-Type": "application/json"
          }
        })
      } catch (error) {
        // 忽略网络错误，专注于测试是否导致服务器崩溃
      }
      
      // 每10次请求检查一次服务器是否还活着
      if (i % 10 === 0) {
        try {
          const healthCheck = await request.get("http://localhost:2022/health")
          console.log(`第 ${i} 次迭代后服务器状态:`, healthCheck.status())
        } catch (error) {
          console.log(`第 ${i} 次迭代后服务器检查失败`)
        }
      }
    }
    
    // 最终健康检查
    try {
      const finalHealthCheck = await request.get("http://localhost:2022/health")
      console.log("压力测试后服务器最终状态:", finalHealthCheck.status())
    } catch (error) {
      console.log("压力测试后服务器检查失败，可能存在内存泄漏或性能问题")
    }
  })
}) 