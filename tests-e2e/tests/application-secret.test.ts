import { test, expect } from "@playwright/test"

test.setTimeout(30000)

test.describe("应用密钥安全功能", () => {
  let createdAppId: string
  let originalSecret: string

  test.beforeAll(async ({ browser }) => {
    // 这里可以添加测试前的初始化，如登录等
    console.log("开始测试应用密钥安全功能...")
  })

  test.afterAll(async ({ browser }) => {
    // 清理测试数据
    console.log("测试完成，清理数据...")
  })

  test("应用创建时显示明文密钥", async ({ page }) => {
    // 导航到登录页面
    await page.goto("/login")
    
    // 登录（需要根据实际登录逻辑调整）
    await page.fill('input[type="phone"]', "13800138000")
    await page.fill('input[type="password"]', "123456")
    await page.click('button[type="submit"]')
    
    // 等待登录完成，导航到应用列表
    await page.waitForURL("**/apps")
    
    // 点击创建应用按钮
    await page.click('text=创建应用')
    
    // 等待创建页面加载
    await page.waitForURL("**/apps/create")
    
    // 填写应用信息
    await page.fill('input[name="name"]', "测试应用密钥")
    await page.fill('textarea[name="description"]', "测试密钥加密功能的应用")
    
    // 提交创建
    await page.click('button[type="submit"]')
    
    // 等待创建成功页面
    await page.waitForSelector('text=应用创建成功')
    
    // 验证密钥显示
    const secretElement = page.locator('code').filter({ hasText: /sk_/ })
    await expect(secretElement).toBeVisible()
    
    // 获取明文密钥
    originalSecret = await secretElement.textContent() || ""
    console.log("创建的密钥:", originalSecret)
    
    // 验证密钥格式（应该以 sk_ 开头）
    expect(originalSecret).toMatch(/^sk_/)
    expect(originalSecret.length).toBeGreaterThan(20)
    
    // 点击眼睛图标隐藏密钥
    await page.click('[data-testid="toggle-secret"]')
    
    // 验证密钥被隐藏
    const hiddenSecret = await secretElement.textContent()
    expect(hiddenSecret).toMatch(/^\*+$/)
    
    // 复制密钥功能测试
    await page.click('[data-testid="toggle-secret"]') // 重新显示
    await page.click('[data-testid="copy-secret"]')
    
    // 验证复制成功提示（如果有的话）
    await expect(page.locator('text=已复制到剪贴板')).toBeVisible({ timeout: 5000 })
    
    // 获取应用ID用于后续测试
    const appIdElement = page.locator('code').filter({ hasText: /app_/ })
    createdAppId = await appIdElement.textContent() || ""
    console.log("创建的应用ID:", createdAppId)
  })

  test("应用列表中密钥已加密", async ({ page }) => {
    // 导航到应用列表
    await page.goto("/apps")
    
    // 等待列表加载
    await page.waitForSelector('table')
    
    // 查找刚创建的应用
    const appRow = page.locator('tr').filter({ hasText: "测试应用密钥" })
    await expect(appRow).toBeVisible()
    
    // 验证密钥列不显示真实密钥（应该显示加密提示）
    const secretCell = appRow.locator('td').filter({ hasText: /密钥已加密|[\*]+/ })
    await expect(secretCell).toBeVisible()
    
    // 确保不显示真实的 sk_ 开头的密钥
    await expect(appRow.locator('text=sk_')).not.toBeVisible()
  })

  test("应用详情页密钥重新生成功能", async ({ page }) => {
    // 导航到应用详情页
    await page.goto(`/apps/${createdAppId}`)
    
    // 等待页面加载
    await page.waitForSelector('text=应用信息')
    
    // 验证当前密钥显示状态
    const secretDisplay = page.locator('text=密钥已加密存储，无法显示')
    await expect(secretDisplay).toBeVisible()
    
    // 点击重新生成密钥按钮
    await page.click('text=重新生成')
    
    // 等待确认对话框
    await page.waitForSelector('text=重新生成密钥')
    await expect(page.locator('text=您确定要重新生成应用密钥吗')).toBeVisible()
    
    // 确认重新生成
    await page.click('button:has-text("重新生成"):last-child')
    
    // 等待成功提示
    await expect(page.locator('text=密钥重新生成成功')).toBeVisible({ timeout: 10000 })
    
    // 验证新密钥显示
    await page.waitForSelector('text=新密钥已生成，请立即保存')
    
    // 验证新密钥可见
    const newSecretElement = page.locator('code').filter({ hasText: /sk_/ })
    await expect(newSecretElement).toBeVisible()
    
    // 获取新密钥
    const newSecret = await newSecretElement.textContent() || ""
    console.log("新生成的密钥:", newSecret)
    
    // 验证新密钥与原密钥不同
    expect(newSecret).not.toBe(originalSecret)
    expect(newSecret).toMatch(/^sk_/)
    expect(newSecret.length).toBeGreaterThan(20)
    
    // 测试眼睛图标功能
    const toggleButton = page.locator('[data-testid="toggle-secret"]')
    await expect(toggleButton).toBeVisible()
    
    await toggleButton.click()
    // 验证密钥被隐藏
    await expect(page.locator('text=' + newSecret)).not.toBeVisible()
    
    await toggleButton.click()
    // 验证密钥重新显示
    await expect(page.locator('text=' + newSecret)).toBeVisible()
    
    // 测试复制功能
    await page.click('[data-testid="copy-secret"]')
    await expect(page.locator('text=已复制到剪贴板')).toBeVisible({ timeout: 5000 })
  })

  test("API 认证使用新密钥", async ({ page, request }) => {
    // 测试使用新密钥进行 API 认证
    const response = await request.post("/api/openapi/auth.authenticate", {
      data: {
        appId: createdAppId,
        secret: "旧密钥应该无效"
      }
    })
    
    // 验证旧密钥认证失败
    expect(response.status()).toBe(401)
    
    // 这里可以添加使用新密钥的认证测试
    // 但需要从页面获取新密钥，或者通过其他方式获取
  })

  test("清理测试数据", async ({ page }) => {
    // 删除测试创建的应用
    await page.goto("/apps")
    
    // 找到测试应用并删除
    const appRow = page.locator('tr').filter({ hasText: "测试应用密钥" })
    await appRow.locator('button').filter({ hasText: "删除" }).click()
    
    // 确认删除
    await page.waitForSelector('text=确认删除')
    await page.click('button:has-text("删除"):last-child')
    
    // 验证删除成功
    await expect(page.locator('text=应用删除成功')).toBeVisible({ timeout: 5000 })
    
    // 验证应用不再存在于列表中
    await expect(page.locator('text=测试应用密钥')).not.toBeVisible()
  })
})

test.describe("边界条件测试", () => {
  test("无效的 appId 认证测试", async ({ request }) => {
    const response = await request.post("/api/openapi/auth.authenticate", {
      data: {
        appId: "invalid_app_id",
        secret: "any_secret"
      }
    })
    
    expect(response.status()).toBe(401)
    const responseData = await response.json()
    expect(responseData.message).toContain("无效的 App ID")
  })

  test("无效的 secret 认证测试", async ({ request }) => {
    // 这里需要一个有效的 appId，可以考虑在测试前创建
    const response = await request.post("/api/openapi/auth.authenticate", {
      data: {
        appId: "app_validappid",
        secret: "invalid_secret"
      }
    })
    
    expect(response.status()).toBe(401)
    const responseData = await response.json()
    expect(responseData.message).toContain("无效的 Secret")
  })
})

test.describe("性能测试", () => {
  test("密钥生成性能测试", async ({ page }) => {
    await page.goto("/apps/create")
    
    const startTime = Date.now()
    
    // 创建应用
    await page.fill('input[name="name"]', "性能测试")
    await page.click('button[type="submit"]')
    
    // 等待创建完成
    await page.waitForSelector('text=应用创建成功')
    
    const endTime = Date.now()
    const duration = endTime - startTime
    
    console.log(`应用创建耗时: ${duration}ms`)
    
    // 验证创建时间在合理范围内（比如5秒以内）
    expect(duration).toBeLessThan(5000)
  })
}) 