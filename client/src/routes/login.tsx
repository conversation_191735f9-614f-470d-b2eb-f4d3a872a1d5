import { createFileRoute, redirect, useNavigate } from '@tanstack/react-router'
import { LoginForm } from '../components/login-form'
import { useAuth } from '../lib/auth/auth-context'
import { useEffect } from 'react'
import { GalleryVerticalEnd } from 'lucide-react'

interface LoginSearch {
  redirect?: string
}

export const Route = createFileRoute('/login')({
  validateSearch: (search: Record<string, unknown>): LoginSearch => {
    return {
      redirect: search?.redirect as string,
    }
  },
  beforeLoad: ({ context }) => {
    // 如果已经登录，重定向到首页
    if (context.auth.isAuthenticated) {
      throw redirect({
        to: '/',
      })
    }
  },
  component: LoginPage,
})

function LoginPage() {
  const { isAuthenticated } = useAuth()
  const navigate = useNavigate({ from: '/login' })
  const { redirect: redirectUrl } = Route.useSearch()

  useEffect(() => {
    if (isAuthenticated) {
      // 登录成功后重定向
      const destination = redirectUrl || '/'
      navigate({ to: destination, replace: true })
    }
  }, [isAuthenticated, navigate, redirectUrl])

  return (
    <div className="grid min-h-svh lg:grid-cols-2">
      <div className="flex flex-col gap-4 p-6 md:p-10">
        <div className="flex justify-center gap-2 md:justify-start">
          <a href="#" className="flex items-center gap-2 font-medium">
            <div className="bg-primary text-primary-foreground flex size-6 items-center justify-center rounded-md">
              <GalleryVerticalEnd className="size-4" />
            </div>
            开放平台
          </a>
        </div>
        <div className="flex flex-1 items-center justify-center">
          <div className="w-full max-w-xs">
            <LoginForm />
          </div>
        </div>
      </div>
      <div className="bg-muted relative hidden lg:block">
        <img
          src="/placeholder.svg"
          alt="Image"
          className="absolute inset-0 h-full w-full object-cover dark:brightness-[0.2] dark:grayscale"
        />
      </div>
    </div>
  )
}
