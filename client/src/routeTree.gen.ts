/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as LoginRouteImport } from './routes/login'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated'
import { Route as AuthenticatedIndexRouteImport } from './routes/_authenticated/index'
import { Route as AuthenticatedRechargeRouteImport } from './routes/_authenticated/recharge'
import { Route as AuthenticatedProfileRouteImport } from './routes/_authenticated/profile'
import { Route as AuthenticatedOrdersRouteImport } from './routes/_authenticated/orders'
import { Route as AuthenticatedAppsIndexRouteImport } from './routes/_authenticated/apps/index'
import { Route as AuthenticatedAppsCreateRouteImport } from './routes/_authenticated/apps/create'
import { Route as AuthenticatedAppsIdIndexRouteImport } from './routes/_authenticated/apps/$id/index'
import { Route as AuthenticatedAppsIdEditRouteImport } from './routes/_authenticated/apps/$id/edit'

const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedIndexRoute = AuthenticatedIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedRechargeRoute = AuthenticatedRechargeRouteImport.update({
  id: '/recharge',
  path: '/recharge',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedProfileRoute = AuthenticatedProfileRouteImport.update({
  id: '/profile',
  path: '/profile',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedOrdersRoute = AuthenticatedOrdersRouteImport.update({
  id: '/orders',
  path: '/orders',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedAppsIndexRoute = AuthenticatedAppsIndexRouteImport.update({
  id: '/apps/',
  path: '/apps/',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedAppsCreateRoute = AuthenticatedAppsCreateRouteImport.update({
  id: '/apps/create',
  path: '/apps/create',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedAppsIdIndexRoute =
  AuthenticatedAppsIdIndexRouteImport.update({
    id: '/apps/$id/',
    path: '/apps/$id/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedAppsIdEditRoute = AuthenticatedAppsIdEditRouteImport.update({
  id: '/apps/$id/edit',
  path: '/apps/$id/edit',
  getParentRoute: () => AuthenticatedRoute,
} as any)

export interface FileRoutesByFullPath {
  '/login': typeof LoginRoute
  '/orders': typeof AuthenticatedOrdersRoute
  '/profile': typeof AuthenticatedProfileRoute
  '/recharge': typeof AuthenticatedRechargeRoute
  '/': typeof AuthenticatedIndexRoute
  '/apps/create': typeof AuthenticatedAppsCreateRoute
  '/apps': typeof AuthenticatedAppsIndexRoute
  '/apps/$id/edit': typeof AuthenticatedAppsIdEditRoute
  '/apps/$id': typeof AuthenticatedAppsIdIndexRoute
}
export interface FileRoutesByTo {
  '/login': typeof LoginRoute
  '/orders': typeof AuthenticatedOrdersRoute
  '/profile': typeof AuthenticatedProfileRoute
  '/recharge': typeof AuthenticatedRechargeRoute
  '/': typeof AuthenticatedIndexRoute
  '/apps/create': typeof AuthenticatedAppsCreateRoute
  '/apps': typeof AuthenticatedAppsIndexRoute
  '/apps/$id/edit': typeof AuthenticatedAppsIdEditRoute
  '/apps/$id': typeof AuthenticatedAppsIdIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_authenticated': typeof AuthenticatedRouteWithChildren
  '/login': typeof LoginRoute
  '/_authenticated/orders': typeof AuthenticatedOrdersRoute
  '/_authenticated/profile': typeof AuthenticatedProfileRoute
  '/_authenticated/recharge': typeof AuthenticatedRechargeRoute
  '/_authenticated/': typeof AuthenticatedIndexRoute
  '/_authenticated/apps/create': typeof AuthenticatedAppsCreateRoute
  '/_authenticated/apps/': typeof AuthenticatedAppsIndexRoute
  '/_authenticated/apps/$id/edit': typeof AuthenticatedAppsIdEditRoute
  '/_authenticated/apps/$id/': typeof AuthenticatedAppsIdIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/login'
    | '/orders'
    | '/profile'
    | '/recharge'
    | '/'
    | '/apps/create'
    | '/apps'
    | '/apps/$id/edit'
    | '/apps/$id'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/login'
    | '/orders'
    | '/profile'
    | '/recharge'
    | '/'
    | '/apps/create'
    | '/apps'
    | '/apps/$id/edit'
    | '/apps/$id'
  id:
    | '__root__'
    | '/_authenticated'
    | '/login'
    | '/_authenticated/orders'
    | '/_authenticated/profile'
    | '/_authenticated/recharge'
    | '/_authenticated/'
    | '/_authenticated/apps/create'
    | '/_authenticated/apps/'
    | '/_authenticated/apps/$id/edit'
    | '/_authenticated/apps/$id/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AuthenticatedRoute: typeof AuthenticatedRouteWithChildren
  LoginRoute: typeof LoginRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/recharge': {
      id: '/_authenticated/recharge'
      path: '/recharge'
      fullPath: '/recharge'
      preLoaderRoute: typeof AuthenticatedRechargeRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/profile': {
      id: '/_authenticated/profile'
      path: '/profile'
      fullPath: '/profile'
      preLoaderRoute: typeof AuthenticatedProfileRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/orders': {
      id: '/_authenticated/orders'
      path: '/orders'
      fullPath: '/orders'
      preLoaderRoute: typeof AuthenticatedOrdersRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/apps/': {
      id: '/_authenticated/apps/'
      path: '/apps'
      fullPath: '/apps'
      preLoaderRoute: typeof AuthenticatedAppsIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/apps/create': {
      id: '/_authenticated/apps/create'
      path: '/apps/create'
      fullPath: '/apps/create'
      preLoaderRoute: typeof AuthenticatedAppsCreateRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/apps/$id/': {
      id: '/_authenticated/apps/$id/'
      path: '/apps/$id'
      fullPath: '/apps/$id'
      preLoaderRoute: typeof AuthenticatedAppsIdIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/apps/$id/edit': {
      id: '/_authenticated/apps/$id/edit'
      path: '/apps/$id/edit'
      fullPath: '/apps/$id/edit'
      preLoaderRoute: typeof AuthenticatedAppsIdEditRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
  }
}

interface AuthenticatedRouteChildren {
  AuthenticatedOrdersRoute: typeof AuthenticatedOrdersRoute
  AuthenticatedProfileRoute: typeof AuthenticatedProfileRoute
  AuthenticatedRechargeRoute: typeof AuthenticatedRechargeRoute
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
  AuthenticatedAppsCreateRoute: typeof AuthenticatedAppsCreateRoute
  AuthenticatedAppsIndexRoute: typeof AuthenticatedAppsIndexRoute
  AuthenticatedAppsIdEditRoute: typeof AuthenticatedAppsIdEditRoute
  AuthenticatedAppsIdIndexRoute: typeof AuthenticatedAppsIdIndexRoute
}

const AuthenticatedRouteChildren: AuthenticatedRouteChildren = {
  AuthenticatedOrdersRoute: AuthenticatedOrdersRoute,
  AuthenticatedProfileRoute: AuthenticatedProfileRoute,
  AuthenticatedRechargeRoute: AuthenticatedRechargeRoute,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
  AuthenticatedAppsCreateRoute: AuthenticatedAppsCreateRoute,
  AuthenticatedAppsIndexRoute: AuthenticatedAppsIndexRoute,
  AuthenticatedAppsIdEditRoute: AuthenticatedAppsIdEditRoute,
  AuthenticatedAppsIdIndexRoute: AuthenticatedAppsIdIndexRoute,
}

const AuthenticatedRouteWithChildren = AuthenticatedRoute._addFileChildren(
  AuthenticatedRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  AuthenticatedRoute: AuthenticatedRouteWithChildren,
  LoginRoute: LoginRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
