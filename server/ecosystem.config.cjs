module.exports = {
  apps: [
    {
      name: 'coozf-server',
      script: 'dist/index.js',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',

      // 开发环境配置
      env: {
        NODE_ENV: 'development',
        // 其他环境变量会从 .env.development 文件中读取
      },

      // 生产环境配置
      env_production: {
        NODE_ENV: 'production',
        // 关键环境变量可以在这里覆盖，其他从 .env.production 读取
        PORT: 8080,
      },

      // 测试环境配置
      env_test: {
        NODE_ENV: 'test',
      },
    },
  ],
}
