{"openapi": "3.0.1", "info": {"title": "云发布", "description": "", "version": "1.0.0"}, "tags": [{"name": "任务"}, {"name": "查询前置配置数据"}, {"name": "媒体号"}], "paths": {"/task/push": {"post": {"summary": "推送发布任务", "deprecated": false, "description": "", "operationId": "TaskController_pushTask", "tags": ["任务"], "parameters": [{"name": "token", "in": "header", "description": "", "required": false, "example": "da6d70afcb8e9244617471bc944cf992", "schema": {"type": "string"}}, {"name": "timestamp", "in": "header", "description": "", "required": false, "example": "12345", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": {"publishType": "video", "formData": {"accounts": [{"accountId": "683fc3029e0d16d1e46d9f61", "cover": {"key": "", "width": 720, "height": 1280, "size": 66870, "path": "https://yixiaoer-image-oss.yixiaoer.cn/********/2c080895-352b-4b18-9d6c-8fba32dca749.jpg"}, "video": {"key": "", "duration": 12.097, "width": 720, "height": 1280, "size": 3766738, "path": "https://globalimg.sucai999.com/uploadfile/********/267440/132835268627695428.mp4"}}], "aPlatform": {"title": "短短十秒的视频冒雨下水用两个小时拍摄", "description": ""}, "bPlatform": {"title": "短短十秒的视频冒雨下水用两个小时拍摄", "description": "", "tags": []}, "isOriginal": false, "categories": {"知乎": [], "企鹅号": [], "爱奇艺": [], "网易号": [], "一点号": [], "哔哩哔哩": [], "搜狐号": []}, "isDraft": false}, "authToken": "66fa6b65f7867d1b4448a0c5:6710a3640b1b1ef89ff7786b", "data": [{"taskId": "683fc3e40795b3d70dbfaafe", "platform": "抖音", "wxkey": null, "videoPath": "https://globalimg.sucai999.com/uploadfile/********/267440/132835268627695428.mp4", "cover": "https://yixiaoer-image-oss.yixiaoer.cn/********/2c080895-352b-4b18-9d6c-8fba32dca749.jpg", "proxy": {"serverAdd": "********", "serverPort": "48471", "type": "kua<PERSON><PERSON>", "userName": null, "password": null, "area": null, "province": null, "city": null, "sid": null}, "platformAccount": {"platformAccountId": "683fc3029e0d16d1e46d9f61", "platformAuthorId": "***********", "platformAccountName": "(˵¯͒〰¯͒˵)(*´I`*)", "platformAccountSpaceId": "683fc3029e0d16d1e46d9f5f", "parentId": null}}]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}, "headers": {}}}, "security": []}}, "/task/queryAuditStatus": {"post": {"summary": "推送审核状态查询任务", "deprecated": false, "description": "", "operationId": "TaskController_pushAuditTask", "tags": ["任务"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuditStatusRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}, "headers": {}}}, "security": []}}, "/task/publishTaskNum": {"get": {"summary": "获取发布中的任务数", "deprecated": false, "description": "", "operationId": "TaskController_getDownloadUrl", "tags": ["任务"], "parameters": [{"name": "token", "in": "header", "description": "", "required": false, "example": "da6d70afcb8e9244617471bc944cf992", "schema": {"type": "string"}}, {"name": "timestamp", "in": "header", "description": "", "required": false, "example": "12345", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}, "headers": {}}}, "security": []}}, "/task/content": {"delete": {"summary": "删除媒体平台上的内容", "deprecated": false, "description": "", "operationId": "TaskController_deletePlatformContent", "tags": ["任务"], "parameters": [{"name": "token", "in": "header", "description": "", "required": false, "example": "da6d70afcb8e9244617471bc944cf992", "schema": {"type": "string"}}, {"name": "timestamp", "in": "header", "description": "", "required": false, "example": "12345", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteContentRequest"}, "example": {"authToken": "66fa6b65f7867d1b4448a0c5:6710a3640b1b1ef89ff7786b", "platform": "腾讯微视", "platformAccountSpaceId": "6821e6b374478a5f26bcf8af", "platformAccountId": "684646f67cdb1672ae14486c", "parentId": "", "wxKey": "", "cookie": "", "docId": "7YfVfT9UJ1UpHle4A", "platformContentType": "video", "isDraft": false}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}, "headers": {}}}, "security": []}}, "/task/{taskId}": {"patch": {"summary": "取消发布中的任务", "deprecated": false, "description": "", "operationId": "TaskController_cancelTask", "tags": ["任务"], "parameters": [{"name": "taskId", "in": "path", "description": "", "required": true, "example": "68495d6e4cc3d41ec4e726ad", "schema": {"type": "string"}}, {"name": "token", "in": "header", "description": "", "required": false, "example": "da6d70afcb8e9244617471bc944cf992", "schema": {"type": "string"}}, {"name": "timestamp", "in": "header", "description": "", "required": false, "example": "12345", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseResponse"}}}, "headers": {}}}, "security": []}}, "/configData/searchMusic": {"post": {"summary": "获取音乐", "deprecated": false, "description": "", "operationId": "ConfigDataController_getMusic", "tags": ["查询前置配置数据"], "parameters": [{"name": "token", "in": "header", "description": "", "required": false, "example": "da6d70afcb8e9244617471bc944cf992", "schema": {"type": "string"}}, {"name": "timestamp", "in": "header", "description": "", "required": false, "example": "12345", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigDataRequest"}, "example": {"authToken": "66fa6b65f7867d1b4448a0c5:673ac4d2543415359ed6cb28", "platform": "抖音", "platformAccountSpaceId": "682155269ca1de958013cf40", "parentId": "", "keyWord": "爱你", "categoryId": "6756837234101455624", "platformAccountId": "682155269ca1de958013cf42", "wxKey": "GzS5XDN-patsOW31y9EzG"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MusicDataResponse"}}}, "headers": {}}}, "security": []}}, "/configData/searchLocation": {"post": {"summary": "获取地理位置", "deprecated": false, "description": "", "operationId": "ConfigDataController_getLocation", "tags": ["查询前置配置数据"], "parameters": [{"name": "token", "in": "header", "description": "", "required": false, "example": "da6d70afcb8e9244617471bc944cf992", "schema": {"type": "string"}}, {"name": "timestamp", "in": "header", "description": "", "required": false, "example": "12345", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigDataRequest"}, "example": {"authToken": "66fa6b65f7867d1b4448a0c5:670499c9fa99b203b6b54efd", "platform": "视频号", "platformAccountSpaceId": "680f7e35e3161434d026bf9f", "platformAccountId": "68520e319b85583ec6df702c", "keyWord": "收费站", "parentId": "", "wxKey": "y9n-b0L6CH4pwpJYeybV4", "proxy": {"serverAdd": "127.0.0.1", "serverPort": 4850}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationDataResponse"}}}, "headers": {}}}, "security": []}}, "/configData/getMusicCategory": {"post": {"summary": "获取音乐分类", "deprecated": false, "description": "", "operationId": "ConfigDataController_getMusicCategory", "tags": ["查询前置配置数据"], "parameters": [{"name": "token", "in": "header", "description": "", "required": false, "example": "da6d70afcb8e9244617471bc944cf992", "schema": {"type": "string"}}, {"name": "timestamp", "in": "header", "description": "", "required": false, "example": "12345", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseDataRequest"}, "example": {"authToken": "66fa6b65f7867d1b4448a0c5:670499c9fa99b203b6b54efd", "platform": "抖音", "platformAccountSpaceId": "680f7e35e3161434d026bf9f", "platformAccountId": "680f7e35e3161434d026bfa1", "keyWord": "欢笑", "parentId": "680e2a9dc28713ce447ca98e", "wxKey": "y9n-b0L6CH4pwpJYeybV4"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryDataResponse"}}}, "headers": {}}}, "security": []}}, "/configData/getMusicByCategory": {"post": {"summary": "根据分类获取音乐", "deprecated": false, "description": "", "operationId": "ConfigDataController_getCategoryByMusic", "tags": ["查询前置配置数据"], "parameters": [{"name": "token", "in": "header", "description": "", "required": false, "example": "da6d70afcb8e9244617471bc944cf992", "schema": {"type": "string"}}, {"name": "timestamp", "in": "header", "description": "", "required": false, "example": "12345", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataByCategoryRequest"}, "example": {"authToken": "66fa6b65f7867d1b4448a0c5:673ac4d2543415359ed6cb28", "platform": "抖音", "platformAccountSpaceId": "682155269ca1de958013cf40", "parentId": "", "categoryId": "6756837234101455624", "platformAccountId": "682155269ca1de958013cf42", "wxKey": "GzS5XDN-patsOW31y9EzG"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MusicDataResponse"}}}, "headers": {}}}, "security": []}}, "/platformAccount/check-status": {"post": {"summary": "检测登录有效性", "deprecated": false, "description": "", "operationId": "PlatformAccountController_getMusicCategory", "tags": ["媒体号"], "parameters": [{"name": "token", "in": "header", "description": "", "required": false, "example": "da6d70afcb8e9244617471bc944cf992", "schema": {"type": "string"}}, {"name": "timestamp", "in": "header", "description": "", "required": false, "example": "12345", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseDataRequest"}, "example": {"authToken": "66fa6b65f7867d1b4448a0c5:673ac4d2543415359ed6cb28", "platform": "抖音", "platformAccountSpaceId": "6821e1be74478a5f26bced66", "parentId": "", "platformAccountId": "682155269ca1de958013cf42", "wxKey": "GzS5XDN-patsOW31y9EzG", "cookie": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountLoginStatusResponse"}}}, "headers": {}}}, "security": []}}}, "components": {"schemas": {"BaseResponse": {"type": "object", "properties": {"statusCode": {"type": "number", "readOnly": true, "examples": [0]}, "message": {"type": "string", "readOnly": true, "examples": ["异常"]}}, "required": ["statusCode", "message"]}, "ConfigDataRequest": {"type": "object", "properties": {"authToken": {"type": "string", "examples": ["调用lite服务的token"]}, "platform": {"type": "string", "examples": ["所属平台，如”抖音“,\"快手\"，具体参考Platforms枚举定义"]}, "platformAccountSpaceId": {"type": "string", "examples": ["用户查询数据的媒体号空间id，用于获取媒体号cookie"]}, "platformAccountId": {"type": "string", "examples": ["媒体号业务系统id"]}, "parentId": {"type": "string", "examples": ["视频号子账号父（微信）id"]}, "wxKey": {"type": "string", "examples": ["视频号子账号父微信key"]}, "cookie": {"type": "string", "examples": ["cookie字符串，视频号子账号只有一个cookie的value字符串，其他平台传正常cookie字符串"]}, "keyWord": {"type": "string", "examples": ["搜索关键词,可空，不分平台为空时没有默认数据"]}, "nextPage": {"type": "string", "examples": ["下一页参数，会在返回结果中给出，如果返回结果没有，则没有下一页"]}}, "required": ["authToken", "platform", "platformAccountSpaceId", "platformAccountId"]}, "MusicDataResponse": {"type": "object", "properties": {"statusCode": {"type": "number", "readOnly": true, "examples": [0]}, "message": {"type": "string", "readOnly": true, "examples": ["异常"]}, "dataList": {"readOnly": true, "type": "array", "items": {"type": "string"}, "examples": ["结果数据列表"]}, "nextPage": {"type": "string", "readOnly": true, "examples": ["下一页参数，为空或者null时，表示没有下一页"]}}, "required": ["statusCode", "message", "dataList", "nextPage"]}, "LocationDataResponse": {"type": "object", "properties": {"statusCode": {"type": "number", "readOnly": true, "examples": [0]}, "message": {"type": "string", "readOnly": true, "examples": ["异常"]}, "dataList": {"readOnly": true, "type": "array", "items": {"type": "string"}, "examples": ["结果数据列表"]}, "nextPage": {"type": "string", "readOnly": true, "examples": ["下一页参数，为空或者null时，表示没有下一页"]}}, "required": ["statusCode", "message", "dataList", "nextPage"]}, "OssResponse": {"type": "object", "properties": {"statusCode": {"type": "number", "readOnly": true, "examples": [0]}, "message": {"type": "string", "readOnly": true, "examples": ["异常"]}, "data": {"type": "object", "readOnly": true, "examples": [0], "properties": {}}}, "required": ["statusCode", "message", "data"]}, "AuditStatusRequest": {"type": "object", "properties": {"authToken": {"type": "string", "examples": ["调用lite服务的token"]}, "platform": {"type": "string", "examples": ["所属平台，如”抖音“,\"快手\"，具体参考Platforms枚举定义"]}, "platformAccountSpaceId": {"type": "string", "examples": ["用户查询数据的媒体号空间id，用于获取媒体号cookie"]}, "platformAccountId": {"type": "string", "examples": ["媒体号业务系统id"]}, "parentId": {"type": "string", "examples": ["视频号子账号父（微信）id"]}, "wxKey": {"type": "string", "examples": ["视频号子账号父微信key"]}, "cookie": {"type": "string", "examples": ["cookie字符串，视频号子账号只有一个cookie的value字符串，其他平台传正常cookie字符串"]}, "taskId": {"type": "string", "examples": ["子任务id"]}, "publishId": {"type": "string", "examples": ["发布产生的publishId"]}}, "required": ["authToken", "platform", "platformAccountSpaceId", "platformAccountId", "taskId", "publishId"]}, "BaseDataRequest": {"type": "object", "properties": {"authToken": {"type": "string", "examples": ["调用lite服务的token"]}, "platform": {"type": "string", "examples": ["所属平台，如”抖音“,\"快手\"，具体参考Platforms枚举定义"]}, "platformAccountSpaceId": {"type": "string", "examples": ["用户查询数据的媒体号空间id，用于获取媒体号cookie"]}, "platformAccountId": {"type": "string", "examples": ["媒体号业务系统id"]}, "parentId": {"type": "string", "examples": ["视频号子账号父（微信）id"]}, "wxKey": {"type": "string", "examples": ["视频号子账号父微信key"]}, "cookie": {"type": "string", "examples": ["cookie字符串，视频号子账号只有一个cookie的value字符串，其他平台传正常cookie字符串"]}}, "required": ["authToken", "platform", "platformAccountSpaceId", "platformAccountId"]}, "CategoryDataResponse": {"type": "object", "properties": {"statusCode": {"type": "number", "readOnly": true, "examples": [0]}, "message": {"type": "string", "readOnly": true, "examples": ["异常"]}, "dataList": {"readOnly": true, "type": "array", "items": {"type": "string"}, "examples": ["分类结果数据列表"]}}, "required": ["statusCode", "message", "dataList"]}, "DataByCategoryRequest": {"type": "object", "properties": {"authToken": {"type": "string", "examples": ["调用lite服务的token"]}, "platform": {"type": "string", "examples": ["所属平台，如”抖音“,\"快手\"，具体参考Platforms枚举定义"]}, "platformAccountSpaceId": {"type": "string", "examples": ["用户查询数据的媒体号空间id，用于获取媒体号cookie"]}, "platformAccountId": {"type": "string", "examples": ["媒体号业务系统id"]}, "parentId": {"type": "string", "examples": ["视频号子账号父（微信）id"]}, "wxKey": {"type": "string", "examples": ["视频号子账号父微信key"]}, "cookie": {"type": "string", "examples": ["cookie字符串，视频号子账号只有一个cookie的value字符串，其他平台传正常cookie字符串"]}, "categoryId": {"type": "string", "examples": ["分类id"]}, "categoryName": {"type": "string", "examples": ["分类名称"]}, "nextPage": {"type": "string", "examples": ["下一页参数，会在返回结果中给出，如果返回结果没有，则没有下一页"]}}, "required": ["authToken", "platform", "platformAccountSpaceId", "platformAccountId", "categoryId", "categoryName"]}, "AccountLoginStatusResponse": {"type": "object", "properties": {"statusCode": {"type": "number", "readOnly": true, "examples": [0]}, "message": {"type": "string", "readOnly": true, "examples": ["异常"]}, "loginStatus": {"type": "number", "readOnly": true, "examples": ["0:失效，1:有效,-1:不确定,-2:不支持的平台，-3：获取cookie失败"]}}, "required": ["statusCode", "message", "loginStatus"]}, "DeleteContentRequest": {"type": "object", "properties": {"authToken": {"type": "string", "examples": ["调用lite服务的token"]}, "platform": {"type": "string", "examples": ["所属平台，如”抖音“,\"快手\"，具体参考Platforms枚举定义"]}, "platformAccountSpaceId": {"type": "string", "examples": ["用户查询数据的媒体号空间id，用于获取媒体号cookie"]}, "platformAccountId": {"type": "string", "examples": ["媒体号业务系统id"]}, "parentId": {"type": "string", "examples": ["视频号子账号父（微信）id"]}, "wxKey": {"type": "string", "examples": ["视频号子账号父微信key"]}, "cookie": {"type": "string", "examples": ["cookie字符串，视频号子账号只有一个cookie的value字符串，其他平台传正常cookie字符串"]}, "docId": {"type": "string", "examples": ["状态查询中的docId"]}, "platformContentType": {"type": "string", "examples": ["内容类型，视频/文章/图文/音频-->article,video,dynamic,audio"]}, "isDraft": {"type": "boolean", "examples": ["是否为草稿"]}}, "required": ["authToken", "platform", "platformAccountSpaceId", "platformAccountId", "docId", "platformContentType", "isDraft"]}}, "securitySchemes": {}}, "servers": []}