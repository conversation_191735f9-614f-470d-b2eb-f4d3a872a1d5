import { randomBytes } from 'crypto'
import redis from './redis'

export interface SessionTokenData {
  userId: string
  applicationId: string
  createdAt: number
}

// 生成一个安全的、随机的 session_token
export async function generateSessionToken(userId: string, applicationId: string): Promise<string> {
  // 2. 生成一个安全的、随机的 session_token
  const sessionToken = `sess_${randomBytes(32).toString('hex')}`
  const redisKey = `session_token:${sessionToken}`

  // 3. 准备存入 Redis 的数据
  const tokenData: SessionTokenData = {
    userId,
    applicationId,
    createdAt: Date.now(),
  }

  // 4. 将 session_token 及其关联数据存入 Redis，并设置过期时间（例如1小时）
  await redis.set(redisKey, JSON.stringify(tokenData), 'EX', 7200) // EX 7200 = 2 hours

  return sessionToken
}

// 获取session_token
export async function getSessionToken(sessionToken: string): Promise<SessionTokenData> {
  const redisKey = `session_token:${sessionToken}`
  const tokenData = await redis.get(redisKey)
  if (!tokenData) {
    throw new Error('Session token not found')
  }
  return JSON.parse(tokenData)
}
