import { pgTable, index } from 'drizzle-orm/pg-core'
import { createInsertSchema, createSelectSchema } from 'drizzle-zod'
import { z } from 'zod'
import { timestamps } from './columns.helpers'
import { applications } from './application'

// 应用蚁贝账户表 (蚁贝属于应用，不是用户)
export const applicationBalances = pgTable('application_balances', (t) => ({
  id: t.uuid().notNull().primaryKey().defaultRandom(),
  applicationId: t
    .uuid()
    .notNull()
    .references(() => applications.id, { onDelete: 'cascade' })
    .unique(),
  balance: t.numeric({ precision: 10, scale: 2 }).default('0.00').notNull(), // 蚁贝余额
  ...timestamps,
}))

// 交易流水表
export const transactions = pgTable(
  'transactions',
  (t) => ({
    id: t.uuid().notNull().primaryKey().defaultRandom(),
    applicationId: t
      .uuid()
      .notNull()
      .references(() => applications.id, { onDelete: 'cascade' }),
    type: t.varchar({ length: 20 }).notNull(), // RECHARGE(充值), CONSUME(消费), REFUND(退款)
    amount: t.numeric({ precision: 10, scale: 2 }).notNull(), // 交易金额（正数为增加，负数为减少）
    beforeBalance: t.numeric({ precision: 10, scale: 2 }).notNull(), // 交易前余额
    afterBalance: t.numeric({ precision: 10, scale: 2 }).notNull(), // 交易后余额
    description: t.varchar({ length: 500 }), // 交易描述
    relatedId: t.uuid().notNull(),
    relatedType: t.varchar({ length: 20 }).notNull(), // 'order' | 'api_call' | 'traffic' | 'refund' 等
    ...timestamps,
  }),
  (table) => [
    index('transactions_app_id_idx').on(table.applicationId),
    index('transactions_type_idx').on(table.type),
    index('transactions_created_at_idx').on(table.createdAt),
  ]
)

// API调用记录表
export const apiCalls = pgTable(
  'api_calls',
  (t) => ({
    id: t.uuid().notNull().primaryKey().defaultRandom(),
    applicationId: t
      .uuid()
      .notNull()
      .references(() => applications.id, { onDelete: 'cascade' }),
    endpoint: t.varchar({ length: 255 }).notNull(), // API端点
    method: t.varchar({ length: 10 }).notNull(), // HTTP方法
    costType: t.varchar({ length: 20 }).notNull(), // ACCOUNT_QUOTA(账号额度), TRAFFIC(流量)
    costAmount: t.numeric({ precision: 10, scale: 2 }).notNull(), // 扣费金额
    requestSize: t.bigint({ mode: 'number' }), // 请求大小(字节)
    responseSize: t.bigint({ mode: 'number' }), // 响应大小(字节)
    statusCode: t.integer(), // HTTP状态码
    ...timestamps,
  }),
  (table) => [
    index('api_calls_app_id_idx').on(table.applicationId),
    index('api_calls_endpoint_idx').on(table.endpoint),
    index('api_calls_cost_type_idx').on(table.costType),
    index('api_calls_created_at_idx').on(table.createdAt),
  ]
)

// 类型推导
export type ApplicationBalance = typeof applicationBalances.$inferSelect
export type InsertApplicationBalance = typeof applicationBalances.$inferInsert
export type Transaction = typeof transactions.$inferSelect
export type InsertTransaction = typeof transactions.$inferInsert
export type ApiCall = typeof apiCalls.$inferSelect
export type InsertApiCall = typeof apiCalls.$inferInsert

// 交易类型枚举
export const TransactionType = {
  RECHARGE: 'RECHARGE', // 充值
  CONSUME: 'CONSUME', // 消费
  REFUND: 'REFUND', // 退款
} as const

export type TransactionTypeEnum = (typeof TransactionType)[keyof typeof TransactionType]

// API调用成本类型枚举
export const ApiCostType = {
  TRAFFIC: 'TRAFFIC', // 流量
  API_CALL: 'API_CALL', // 接口调用
} as const

export type ApiCostTypeEnum = (typeof ApiCostType)[keyof typeof ApiCostType]

// Zod Schema
export const CreateTransactionSchema = createInsertSchema(transactions, {
  amount: z.number().min(0.01, '金额必须大于0'),
  type: z.enum(['RECHARGE', 'CONSUME', 'REFUND']),
  description: z.string().max(500, '描述不能超过500个字符').optional(),
}).omit({
  id: true,
  beforeBalance: true,
  afterBalance: true,
  createdAt: true,
  updatedAt: true,
})

export const CreateApiCallSchema = createInsertSchema(apiCalls, {
  endpoint: z.string().min(1, 'API端点不能为空'),
  method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']),
  costType: z.enum(['ACCOUNT_QUOTA', 'TRAFFIC']),
  costAmount: z.number().min(0, '扣费金额不能为负数'),
}).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
})

export const SelectTransactionSchema = createSelectSchema(transactions)
export const SelectApiCallSchema = createSelectSchema(apiCalls)

// 交易记录查询参数
export const TransactionListSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  applicationId: z.string().uuid().optional(),
  type: z.enum(['RECHARGE', 'CONSUME', 'REFUND']).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

// API调用记录查询参数
export const ApiCallListSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  applicationId: z.string().uuid().optional(),
  endpoint: z.string().optional(),
  costType: z.enum(['TRAFFIC', 'API_CALL']).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

export type TransactionListParams = z.infer<typeof TransactionListSchema>
export type ApiCallListParams = z.infer<typeof ApiCallListSchema>

export type NewTransaction = typeof transactions.$inferInsert

// relatedType
export type RelatedType = 'order' | 'api_call' | 'traffic' | 'refund'
