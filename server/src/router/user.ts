import { z } from 'zod'
import { router } from '@/trpc'
import { users } from '@/db/schema'
import { eq } from 'drizzle-orm'
import { protectedProcedure } from '@/procedure'

export const userRouter = router({
  // 获取个人信息
  user: protectedProcedure.query(async ({ ctx }) => {
    return ctx.user
  }),

  // 更新个人信息
  update: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1, '姓名不能为空').optional(),
        avatar: z.string().url('请输入有效的头像地址').optional(),
      })
    )
    .mutation(({ ctx, input }) => {
      // 只更新允许的字段
      const updateData = {
        name: input.name,
        avatar: input.avatar,
      }
      return ctx.db.update(users).set(updateData).where(eq(users.id, ctx.user.id)).returning()
    }),
})
