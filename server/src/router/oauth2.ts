import { TRPCError } from '@trpc/server'
import { z } from 'zod'
import { eq, and } from 'drizzle-orm'
import { router } from '@/trpc'
import { openAPIProcedure } from '@/procedure'
import { authAccounts, OAuthAuthorizeSchema, ApiProxySchema } from '@/db/schema'
import { SUPPORTED_PLATFORMS, generateOAuthState, generateAuthorizeUrl, proxyPlatformApi } from '@/lib/oauth2'


export const oauth2Router = router({
  // 生成OAuth授权URL
  generateAuthorizeUrl: openAPIProcedure
    .meta({
      openapi: {
        method: 'POST',
        path: '/oauth2/authorize',
        summary: '生成OAuth授权URL',
        description: '为指定平台生成OAuth授权URL，用户点击后跳转到对应平台授权页面',
        tags: ['OAuth2'],
      },
    })
    .input(OAuthAuthorizeSchema)
    .output(
      z.object({
        authorizeUrl: z.string().url('授权URL'),
        state: z.string().describe('状态参数'),
        platform: z.string().describe('平台标识'),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { platform } = input

      if (!ctx.application.webhookUrl) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '应用未配置Webhook URL，请先在应用设置中配置',
        })
      }

      // 生成回调URL（指向我们的服务器）
      const redirectUri = `https://1c3e-118-249-196-233.ngrok-free.app/api/oauth2/${platform}/callback`

      // 生成带有应用和平台信息的状态参数
      const state = generateOAuthState(ctx.application.id, platform)

      // 生成授权URL
      const authorizeUrl = generateAuthorizeUrl(platform, redirectUri, state)

      // 临时存储授权信息到数据库（有效期15分钟）
      try {
        await ctx.db.insert(authAccounts).values({
          appId: ctx.application.id,
          platform,
          platformUserId: 'temp_' + state, // 临时占位
          state,
          scope: 'basic_info',
          userInfo: { temp: true, timestamp: Date.now() },
        })
      } catch (error) {
        console.error('存储OAuth临时数据失败:', error)
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '生成授权链接失败',
        })
      }

      return {
        authorizeUrl,
        state,
        platform,
      }
    }),

  // API透传代理
  proxyApi: openAPIProcedure
    .meta({
      openapi: {
        method: 'POST',
        path: '/oauth2/proxy',
        summary: 'OAuth2 API透传代理',
        description: '代理调用各平台开放API，支持所有HTTP方法',
        tags: ['OAuth2代理'],
      },
    })
    .input(
      ApiProxySchema.extend({
        platform: z.enum(SUPPORTED_PLATFORMS, {
          errorMap: () => ({ message: '不支持的平台' }),
        }),
      })
    )
    .output(
      z.object({
        success: z.boolean(),
        data: z.unknown(),
        platform: z.string(),
        raw_response: z.unknown().optional(),
      })
    )
    .mutation(async ({ input }) => {
      const { platform, accessToken, apiPath, method, params, headers } = input

      try {
        // 调用平台API
        const result = await proxyPlatformApi(platform, accessToken, apiPath, method, params, headers)

        // TODO: 后续可以添加API调用计费逻辑
        // 计算请求和响应大小用于流量计费

        return {
          success: result.success,
          data: result.data,
          platform,
          raw_response: result.raw_response,
        }
      } catch (error) {
        console.error(`${platform} API代理调用失败:`, error)

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `API调用失败: ${error instanceof Error ? error.message : '未知错误'}`,
        })
      }
    }),

  // 获取授权记录列表
  getAuthAccounts: openAPIProcedure
    .meta({
      openapi: {
        method: 'GET',
        path: '/oauth2/accounts',
        summary: '获取OAuth授权账号列表',
        description: '获取应用下的OAuth授权账号列表',
        tags: ['OAuth2'],
      },
    })
    .input(
      z.object({
        platform: z
          .enum(SUPPORTED_PLATFORMS, {
            errorMap: () => ({ message: '不支持的平台' }),
          })
          .optional(),
        page: z.number().min(1).default(1),
        pageSize: z.number().min(1).max(100).default(10),
      })
    )
    .output(
      z.object({
        accounts: z.array(
          z.object({
            id: z.string(),
            platform: z.string(),
            platformUserId: z.string(),
            userInfo: z.unknown(),
            scope: z.string().nullable(),
            createdAt: z.string(),
            updatedAt: z.string(),
          })
        ),
        total: z.number(),
        page: z.number(),
        pageSize: z.number(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { platform, page, pageSize } = input
      const offset = (page - 1) * pageSize

      try {
        // 构建查询条件
        const whereConditions = [eq(authAccounts.appId, ctx.application.id)]
        if (platform) {
          whereConditions.push(eq(authAccounts.platform, platform))
        }

        // 查询授权记录（排除临时记录）
        const accounts = await ctx.db.query.authAccounts.findMany({
          where: and(...whereConditions),
          limit: pageSize,
          offset,
          orderBy: (table, { desc }) => [desc(table.createdAt)],
        })

        // 过滤掉临时记录
        const validAccounts = accounts.filter(
          (account) => !account.userInfo?.temp && !account.platformUserId.startsWith('temp_')
        )

        // 获取总数（排除临时记录）
        const totalRecords = await ctx.db.$count(authAccounts, and(...whereConditions))

        return {
          accounts: validAccounts.map((account) => ({
            id: account.id,
            platform: account.platform,
            platformUserId: account.platformUserId,
            userInfo: account.userInfo,
            scope: account.scope,
            createdAt: account.createdAt,
            updatedAt: account.updatedAt,
          })),
          total: totalRecords,
          page,
          pageSize,
        }
      } catch (error) {
        console.error('查询授权记录失败:', error)
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '查询授权记录失败',
        })
      }
    }),

  // 删除授权记录
  deleteAuthAccount: openAPIProcedure
    .meta({
      openapi: {
        method: 'DELETE',
        path: '/oauth2/accounts/{accountId}',
        summary: '删除OAuth授权账号',
        description: '删除指定的OAuth授权账号记录',
        tags: ['OAuth2'],
      },
    })
    .input(
      z.object({
        accountId: z.string().uuid('授权记录ID格式错误'),
      })
    )
    .output(
      z.object({
        success: z.boolean(),
        message: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { accountId } = input

      try {
        // 验证记录存在且属于当前应用
        const account = await ctx.db.query.authAccounts.findFirst({
          where: and(eq(authAccounts.id, accountId), eq(authAccounts.appId, ctx.application.id)),
        })

        if (!account) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '授权记录不存在',
          })
        }

        // 删除记录
        await ctx.db.delete(authAccounts).where(eq(authAccounts.id, accountId))

        return {
          success: true,
          message: '授权记录已删除',
        }
      } catch (error) {
        console.error('删除授权记录失败:', error)

        if (error instanceof TRPCError) {
          throw error
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '删除授权记录失败',
        })
      }
    }),
})
