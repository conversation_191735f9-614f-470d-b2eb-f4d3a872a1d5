import { TRPCError } from '@trpc/server'
import { z } from 'zod'
import { router } from '@/trpc'
import {
  applications,
  CreateApplicationSchema,
  UpdateApplicationSchema,
  ApplicationListSchema,
  apiCalls,
  transactions,
} from '@/db/schema'
import type { ApplicationWithBalance } from '@/db/schema'
import { applicationBalances } from '@/db/balance'
import { eq, and, desc, count, ilike, gte, sum, sql } from 'drizzle-orm'
import { generateUniqueAppId, generateAndHashSecret } from '@/lib/application'
import { formatSecretForDisplay } from '@/lib/crypto'
import { applicationProcedure, applicationWithBalanceProcedure, protectedProcedure } from '@/procedure'

export const applicationRouter = router({
  // 获取应用列表（分页）
  list: protectedProcedure.input(ApplicationListSchema).query(async ({ ctx, input }) => {
    const { page, pageSize, search } = input
    const offset = (page - 1) * pageSize

    // 构建查询条件
    const whereConditions = [eq(applications.userId, ctx.user.id)]

    if (search) {
      whereConditions.push(ilike(applications.name, `%${search}%`))
    }

    // 获取总数
    const [totalResult] = await ctx.db
      .select({ count: count() })
      .from(applications)
      .where(and(...whereConditions))

    const total = totalResult?.count || 0

    // 获取分页数据，关联余额表
    const items = await ctx.db
      .select({
        id: applications.id,
        name: applications.name,
        description: applications.description,
        appId: applications.appId,
        secret: applications.secret,
        status: applications.status,
        webhookUrl: applications.webhookUrl,
        userId: applications.userId,
        createdAt: applications.createdAt,
        updatedAt: applications.updatedAt,
        balance: sql<string>`COALESCE(${applicationBalances.balance}, '0.00')`.as('balance'),
      })
      .from(applications)
      .leftJoin(applicationBalances, eq(applications.id, applicationBalances.applicationId))
      .where(and(...whereConditions))
      .orderBy(desc(applications.createdAt))
      .limit(pageSize)
      .offset(offset)

    // 格式化 Secret 显示
    const formattedItems = items.map((item) => ({
      ...item,
      secret: '****************************', // 隐藏 Secret
      secretDisplay: '密钥已加密存储',
    }))

    return {
      items: formattedItems as ApplicationWithBalance[],
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    }
  }),

  // 创建应用
  create: protectedProcedure.input(CreateApplicationSchema).mutation(async ({ ctx, input }) => {
    // 生成唯一的应用ID和密钥
    const appId = await generateUniqueAppId()
    const { secret, hashedSecret } = await generateAndHashSecret()

    const newApplication = await ctx.db
      .insert(applications)
      .values({
        ...input,
        appId,
        secret: hashedSecret, // 存储加密后的 Secret
        userId: ctx.user.id,
      })
      .returning()

    if (!newApplication[0]) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: '创建应用失败',
      })
    }

    // 返回应用信息，包含明文 Secret（仅此一次）
    return {
      ...newApplication[0],
      secret, // 明文 Secret，仅在创建时返回
      secretDisplay: formatSecretForDisplay(secret), // 格式化显示
    }
  }),

  // 获取单个应用详情
  byId: applicationWithBalanceProcedure.query(async ({ ctx }) => {
    return ctx.applicationWithBalance
  }),

  // 更新应用
  update: applicationProcedure.input(UpdateApplicationSchema).mutation(async ({ ctx, input }) => {
    // 只更新允许的字段
    const filteredUpdateData = {
      ...(input.name && { name: input.name }),
      ...(input.description !== undefined && { description: input.description }),
      ...(input.webhookUrl !== undefined && { webhookUrl: input.webhookUrl }),
    }

    const updated = await ctx.db
      .update(applications)
      .set(filteredUpdateData)
      .where(eq(applications.id, ctx.applicationId))
      .returning()

    if (!updated[0]) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: '更新应用失败',
      })
    }

    return updated[0]
  }),

  // 删除应用
  delete: applicationProcedure.mutation(async ({ ctx }) => {
    await ctx.db.delete(applications).where(eq(applications.id, ctx.applicationId))

    return { success: true }
  }),

  // 重新生成 Secret
  regenerateSecret: applicationProcedure.mutation(async ({ ctx }) => {
    // 生成新的 Secret
    const { secret, hashedSecret } = await generateAndHashSecret()

    // 更新数据库中的 Secret
    const updated = await ctx.db
      .update(applications)
      .set({ secret: hashedSecret })
      .where(eq(applications.id, ctx.applicationId))
      .returning()

    if (!updated[0]) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: '重新生成密钥失败',
      })
    }

    // 返回新的明文 Secret（仅此一次）
    return {
      ...updated[0],
      secret, // 明文 Secret，仅在重新生成时返回
      secretDisplay: formatSecretForDisplay(secret),
      message: '密钥已重新生成，请妥善保存，此密钥不会再次显示',
    }
  }),

  // 获取应用统计数据
  getApplicationStats: applicationProcedure.query(async ({ ctx }) => {
    // 获取API调用统计
    const apiCallStats = await ctx.db
      .select({
        total: count(),
        accountQuotaCount: sum(sql`CASE WHEN ${apiCalls.costType} = 'ACCOUNT_QUOTA' THEN 1 ELSE 0 END`),
        trafficGB: sum(sql`CASE WHEN ${apiCalls.costType} = 'TRAFFIC' THEN ${apiCalls.costAmount} ELSE 0 END`),
      })
      .from(apiCalls)
      .where(eq(apiCalls.applicationId, ctx.applicationId))

    const stats = apiCallStats[0] || { total: 0, accountQuotaCount: 0, trafficGB: 0 }

    // 获取充值总额
    const rechargeStats = await ctx.db
      .select({
        totalRecharge: sum(sql`CASE WHEN ${transactions.type} = 'RECHARGE' THEN ${transactions.amount} ELSE 0 END`),
      })
      .from(transactions)
      .where(eq(transactions.applicationId, ctx.applicationId))

    const rechargeTotal = rechargeStats[0]?.totalRecharge || 0

    return {
      apiCallCount: Number(stats.total) || 0,
      accountCount: Number(stats.accountQuotaCount) || 0,
      trafficGB: Number(stats.trafficGB) || 0,
      totalRecharge: Number(rechargeTotal) || 0,
    }
  }),

  // 获取应用趋势数据
  getApplicationTrends: applicationProcedure
    .input(
      z.object({
        days: z.number().min(1).max(90).default(30),
      })
    )
    .query(async ({ ctx, input }) => {
      const daysAgo = new Date()
      daysAgo.setDate(daysAgo.getDate() - input.days)

      // 获取每日API调用趋势
      const dailyStats = await ctx.db
        .select({
          date: sql`DATE(${apiCalls.createdAt})`.as('date'),
          apiCalls: count(),
          accountCount: sum(sql`CASE WHEN ${apiCalls.costType} = 'ACCOUNT_QUOTA' THEN 1 ELSE 0 END`),
          trafficGB: sum(sql`CASE WHEN ${apiCalls.costType} = 'TRAFFIC' THEN ${apiCalls.costAmount} ELSE 0 END`),
        })
        .from(apiCalls)
        .where(and(eq(apiCalls.applicationId, ctx.applicationId), gte(apiCalls.createdAt, daysAgo.toISOString())))
        .groupBy(sql`DATE(${apiCalls.createdAt})`)
        .orderBy(sql`DATE(${apiCalls.createdAt})`)

      // 填充没有数据的日期
      const trends: Array<{
        date: string
        apiCalls: number
        accountCount: number
        trafficGB: number
      }> = []

      for (let i = input.days - 1; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        const dateStr = date.toISOString().split('T')[0]

        const existing = dailyStats.find((stat) => stat.date === dateStr)
        trends.push({
          date: dateStr!,
          apiCalls: Number(existing?.apiCalls) || 0,
          accountCount: Number(existing?.accountCount) || 0,
          trafficGB: Number(existing?.trafficGB) || 0,
        })
      }

      return trends
    }),
})
