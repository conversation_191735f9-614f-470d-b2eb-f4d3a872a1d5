import { authRouter } from '@/router/auth'
import { userRouter } from '@/router/user'
import { applicationRouter } from '@/router/application'
import { balanceRouter } from '@/router/balance'
import { orderRouter } from '@/router/order'
import { openAuthRouter } from '@/router/openAuth'
// import { oauth2Router } from './oauth2'
import { router } from '@/trpc'

export const appRouter = router({
  auth: authRouter,
  user: userRouter,
  application: applicationRouter,
  balance: balanceRouter,
  order: orderRouter,
  // oauth2: oauth2Router,
})

export const openApiRouter = router({
  auth: openAuthRouter,
  // oauth2: oauth2Router,
})

// export type definition of API
export type AppRouter = typeof appRouter
