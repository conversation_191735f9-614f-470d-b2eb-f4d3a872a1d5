import { applications, users } from '@/db/schema'
import { generateOpenAPIToken } from '@/lib/jwt'
import { verifySecret } from '@/lib/crypto'
import { openAPIProcedure } from '@/procedure'
import { t } from '@/trpc'
import { TRPCError } from '@trpc/server'
import { eq } from 'drizzle-orm'
import { z } from 'zod'
import { generateSessionToken } from '@/lib/session-token'

// 认证请求 Schema
const AuthenticateSchema = z.object({
  appId: z.string().min(1, 'App ID 不能为空').describe('应用唯一标识符，格式：app_xxxxxxxx'),
  secret: z.string().min(1, 'Secret 不能为空').describe('应用密钥，格式：sk_xxxxxxxx'),
})

// 认证响应 Schema
const AuthenticateResponseSchema = z.object({
  access_token: z.string().describe('访问令牌，用于后续API调用的身份验证'),
  token_type: z.string().describe('令牌类型，固定值：Bearer'),
  expires_in: z.number().describe('令牌过期时间，单位：秒'),
  application: z
    .object({
      id: z.string().describe('应用数据库ID'),
      name: z.string().describe('应用名称'),
      appId: z.string().describe('应用唯一标识符'),
    })
    .describe('应用信息'),
  user: z
    .object({
      id: z.string().describe('用户数据库ID'),
      name: z.string().describe('用户名称'),
    })
    .describe('用户信息'),
})

// Token验证响应 Schema
const TokenVerifyResponseSchema = z.object({
  valid: z.boolean().describe('Token是否有效'),
  user: z
    .object({
      id: z.string().describe('用户数据库ID'),
      name: z.string().describe('用户名称'),
      phone: z.string().optional().describe('用户手机号'),
    })
    .describe('用户信息'),
  application: z
    .object({
      id: z.string().describe('应用数据库ID'),
      name: z.string().describe('应用名称'),
      appId: z.string().describe('应用唯一标识符'),
    })
    .describe('应用信息'),
})

// 应用信息响应 Schema
const AppInfoResponseSchema = z.object({
  application: z
    .object({
      id: z.string().describe('应用数据库ID'),
      name: z.string().describe('应用名称'),
      description: z.string().optional().describe('应用描述'),
      appId: z.string().describe('应用唯一标识符'),

      createdAt: z.string().describe('创建时间'),
    })
    .describe('应用详细信息'),
  user: z
    .object({
      id: z.string().describe('用户数据库ID'),
      name: z.string().describe('用户名称'),
    })
    .describe('用户信息'),
})

export const openAuthRouter = t.router({
  // 使用 appId 和 secret 获取 token
  authenticate: t.procedure
    .meta({
      openapi: {
        method: 'POST',
        path: '/auth/authenticate',
        tags: ['OpenAPI认证'],
        summary: '获取访问令牌',
        description: '使用应用ID和密钥获取访问令牌，用于后续OpenAPI调用的身份验证。令牌有效期为30天。',
      },
    })
    .input(AuthenticateSchema)
    .output(AuthenticateResponseSchema)
    .mutation(async ({ input, ctx }) => {
      const { appId, secret } = input

      try {
        // 先根据 appId 查询应用信息
        const application = await ctx.db.query.applications.findFirst({
          where: eq(applications.appId, appId),
        })

        if (!application) {
          throw new TRPCError({
            code: 'UNAUTHORIZED',
            message: '无效的 App ID',
          })
        }

        // 验证 Secret
        const isSecretValid = await verifySecret(secret, application.secret)
        if (!isSecretValid) {
          throw new TRPCError({
            code: 'UNAUTHORIZED',
            message: '无效的 Secret',
          })
        }

        // 查询用户信息
        const user = await ctx.db.query.users.findFirst({
          where: eq(users.id, application.userId),
        })

        if (!user || !user.name) {
          throw new TRPCError({
            code: 'UNAUTHORIZED',
            message: '用户不存在',
          })
        }

        // 生成 token
        const token = generateOpenAPIToken({
          userId: application.userId,
          appId: application.appId,
          type: 'open_api',
        })

        return {
          access_token: token,
          token_type: 'Bearer',
          expires_in: 30 * 24 * 60 * 60, // 30天，单位秒
          application: {
            id: application.id,
            name: application.name,
            appId: application.appId,
          },
          user: {
            id: user.id,
            name: user.name,
          },
        }
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '认证失败',
        })
      }
    }),

  // 验证 token 是否有效（需要使用 openAPIProcedure）
  verify: openAPIProcedure
    .meta({
      openapi: {
        method: 'GET',
        path: '/auth/verify',
        tags: ['OpenAPI认证'],
        summary: '验证访问令牌',
        description: '验证当前访问令牌是否有效，并返回关联的用户和应用信息。',
      },
    })
    .input(z.void().describe('无需输入参数'))
    .output(TokenVerifyResponseSchema)
    .query(async ({ ctx }) => {
      return {
        valid: true,
        user: {
          id: ctx.user.id,
          name: ctx.user.name || '',
          phone: ctx.user.phone || undefined,
        },
        application: {
          id: ctx.application.id,
          name: ctx.application.name,
          appId: ctx.application.appId,
        },
      }
    }),

  // 获取应用信息（需要使用 openAPIProcedure）
  getAppInfo: openAPIProcedure
    .meta({
      openapi: {
        method: 'GET',
        path: '/auth/app-info',
        tags: ['OpenAPI认证'],
        summary: '获取应用信息',
        description: '获取当前访问令牌关联的应用详细信息，包括应用配置、流量使用情况等。',
      },
    })
    .input(z.void().describe('无需输入参数'))
    .output(AppInfoResponseSchema)
    .query(async ({ ctx }) => {
      return {
        application: {
          id: ctx.application.id,
          name: ctx.application.name,
          description: ctx.application.description || undefined,
          appId: ctx.application.appId,

          createdAt: ctx.application.createdAt,
        },
        user: {
          id: ctx.user.id,
          name: ctx.user.name || '',
        },
      }
    }),

  // 获取session_token
  getSessionToken: openAPIProcedure
    .meta({
      openapi: {
        method: 'GET',
        path: '/auth/session-token',
        tags: ['OpenAPI认证'],
        summary: '获取session_token',
        description: '获取当前访问令牌关联的session_token。用户需要将session_token返回给前端，用于后续的API调用。',
      },
    })
    .input(z.void().describe('无需输入参数'))
    .output(
      z.object({
        session_token: z.string().describe('session_token'),
      })
    )
    .query(async ({ ctx }) => {
      const sessionToken = await generateSessionToken(ctx.user.id, ctx.application.id)
      return {
        session_token: sessionToken,
      }
    }),
})
