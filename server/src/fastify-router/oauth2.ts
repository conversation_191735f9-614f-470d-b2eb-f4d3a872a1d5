import type { FastifyInstance } from 'fastify'
import { eq, and } from 'drizzle-orm'
import { authAccounts, applications } from '@/db/schema'
import { parseOAuthState, getAccessToken, sendWebhookNotification } from '@/lib/oauth2'
import { db } from '@/db'

export async function oauth2Routes(app: FastifyInstance) {
  // 通用OAuth2回调处理
  app.get('/:platform/callback', async (request, reply) => {
    const { platform } = request.params as { platform: string }
    const { auth_code: code, state } = request.query as { auth_code?: string; state?: string }

    console.log(`处理 ${platform} OAuth2 回调:`, { code: code?.substring(0, 10) + '...', state })

    // 参数验证
    if (!code || !state) {
      return reply.status(400).send({
        success: false,
        error: '缺少必要参数 code 或 state',
      })
    }

    try {
      // 解析状态参数
      const stateInfo = parseOAuthState(state)
      if (!stateInfo) {
        return reply.status(400).send({
          success: false,
          error: '无效的状态参数',
        })
      }

      const { appId, platform: statePlatform } = stateInfo

      // 验证平台匹配
      if (platform !== statePlatform) {
        return reply.status(400).send({
          success: false,
          error: '平台不匹配',
        })
      }

      const application = await db.query.applications.findFirst({
        where: eq(applications.id, appId),
      })

      if (!application) {
        return reply.status(404).send({
          success: false,
          error: '应用不存在',
        })
      }

      // 通过授权码获取访问令牌
      console.log(`获取 ${platform} 访问令牌...`)
      const tokenResult = await getAccessToken(platform, code)

      if (!tokenResult.success || !tokenResult.data) {
        console.error(`${platform} 获取访问令牌失败:`, tokenResult.error)
        return reply.status(400).send({
          success: false,
          error: tokenResult.error || '获取访问令牌失败',
        })
      }

      const { user_id, access_token, refresh_token, expires_in, scope, name } = tokenResult.data

      // 检查是否已存在该用户的授权记录
      const existingAccount = await db.query.authAccounts.findFirst({
        where: and(
          eq(authAccounts.appId, appId),
          eq(authAccounts.platform, platform),
          eq(authAccounts.platformUserId, user_id)
        ),
      })

      // 更新或创建授权记录
      if (existingAccount) {
        // 更新现有记录
        await db
          .update(authAccounts)
          .set({
            userInfo: tokenResult.data,
            scope: scope,
            state: null, // 清除状态
            updatedAt: new Date().toISOString(),
          })
          .where(eq(authAccounts.id, existingAccount.id))
      } else {
        // 创建新记录，先删除临时记录
        await db.insert(authAccounts).values({
          appId,
          platform,
          platformUserId: user_id,
          userInfo: { name: name },
          scope: scope,
          state: null, // 授权完成后清除状态
        })
      }

      // 发送Webhook通知
      console.log(`发送 ${platform} Webhook通知到:`, application.webhookUrl)
      const webhookSuccess = await sendWebhookNotification(application.webhookUrl || '', {
        platform,
        event: 'authorization_completed',
        user_info: tokenResult.data,
        access_token,
        refresh_token,
        expires_in,
        scope,
        app_id: appId,
        custom_state: stateInfo.customState,
      })

      if (!webhookSuccess) {
        console.warn(`${platform} Webhook通知发送失败`)
      }

      // 返回成功页面
      return reply.type('text/html').send(`
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>${platform} 授权成功</title>
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .success { color: #4CAF50; }
            .info { color: #2196F3; margin: 20px 0; }
          </style>
        </head>
        <body>
          <h1 class="success">✅ ${platform} 授权成功</h1>
          <p class="info">您已成功完成 ${platform} 账号授权</p>
          <p>用户: ${name || user_id}</p>
          <p>您可以关闭此页面</p>
          <script>
            // 3秒后自动关闭窗口
            setTimeout(() => {
              window.close();
            }, 3000);
          </script>
        </body>
        </html>
      `)
    } catch (error) {
      console.error(`${platform} OAuth2回调处理失败:`, error)
      return reply.status(500).send({
        success: false,
        error: `授权处理失败: ${error instanceof Error ? error.message : '未知错误'}`,
      })
    }
  })

  // webhook
  app.post('/webhook', async (request, reply) => {
    console.log(request.body)
    return reply.send({
      success: true,
      message: 'Webhook received',
    })
  })
}
