import type { FastifyInstance } from 'fastify'
import { oauth2Routes } from './oauth2'
import { xiaohongshuWebhookRoutes } from './webhook'
import { crawlerProxyRoutes } from './crawler-porxy'

const routes = (app: FastifyInstance) => {
  app.register(oauth2Routes, { prefix: '/oauth2' })
  app.register(xiaohongshuWebhookRoutes, { prefix: '/webhook' })
  app.register(crawlerProxyRoutes, { prefix: '/crawler' })
}

export default routes
