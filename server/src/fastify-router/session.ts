// src/routes/session.ts
import type { FastifyPluginAsync } from 'fastify'
import { verifyToken } from '@/procedure' // 你现有的、验证客户长效Token的函数
import { randomBytes } from 'crypto'
import redis from '@/lib/redis'

export const sessionRoutes: FastifyPluginAsync = async (fastify) => {
  // 路由：POST /api/v1/session/generate
  // 用途：客户后端调用此接口，为前端/客户端生成一个临时的 session_token
  fastify.post('/session/generate', async (request, reply) => {
    try {
      // 1. 使用你现有的逻辑，验证客户的长效 access_token
      const authHeader = request.headers.authorization
      const userAppData = await verifyToken(authHeader ?? '')

      // 2. 生成一个安全的、随机的 session_token
      const sessionToken = `sess_${randomBytes(32).toString('hex')}`
      const redisKey = `session_token:${sessionToken}`

      // 3. 准备存入 Redis 的数据
      const tokenData = {
        userId: userAppData.user.id, // 如果有最终用户的概念，也一并存入
        applicationId: userAppData.application.id,
        createdAt: Date.now(),
      }

      // 4. 将 session_token 及其关联数据存入 Redis，并设置过期时间（例如1小时）
      await redis.set(redisKey, JSON.stringify(tokenData), 'EX', 3600) // EX 3600 = 1 hour

      // 5. 将 session_token 返回给客户的后端
      return reply.code(200).send({ session_token: sessionToken, expires_in: 3600 })
    } catch (error) {
      fastify.log.error(error)
      return reply.code(500).send({ error: 'Failed to generate session token' })
    }
  })
}
