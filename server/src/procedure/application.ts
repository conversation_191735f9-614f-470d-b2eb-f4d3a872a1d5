import type { Application} from '@/db/application';
import { applications } from '@/db/application'
import { applicationBalances } from '@/db/balance'
import { TRPCError } from '@trpc/server'
import { and, eq, sql } from 'drizzle-orm'
import { LRUCache } from 'lru-cache'
import z from 'zod'
import { protectedProcedure } from '.'

const appPermissionCache = new LRUCache<string, Application>({
  max: 5000,
  ttl: 5 * 60 * 1000, // 5分钟权限缓存
})

export const applicationProcedure = protectedProcedure
  .input(
    z.object({
      applicationId: z.string().uuid(),
    })
  )
  .use(async function isAuthApp({ ctx, next, input }) {
    const { applicationId } = input
    const cacheKey = `${ctx.user.id}:${applicationId}`

    // 检查权限缓存
    if (!appPermissionCache.has(cacheKey)) {
      // 缓存未命中，查询数据库验证权限
      const existingApp = await ctx.db.query.applications.findFirst({
        where: and(eq(applications.id, applicationId), eq(applications.userId, ctx.user.id)),
      })
      if (!existingApp) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '应用不存在或无权限访问',
        })
      }

      // 缓存权限验证结果
      appPermissionCache.set(cacheKey, existingApp)
    }

    return next({
      ctx: {
        ...ctx,
        applicationId,
        application: appPermissionCache.get(cacheKey)!,
      },
    })
  })

export const applicationWithBalanceProcedure = protectedProcedure
  .input(
    z.object({
      applicationId: z.string().uuid(),
    })
  )
  .use(async function isAuthApp({ ctx, next, input }) {
    const { applicationId } = input
    // 一次查询验证权限并获取应用数据
    const result = await ctx.db
      .select({
        id: applications.id,
        name: applications.name,
        description: applications.description,
        appId: applications.appId,
        secret: applications.secret,
        webhookUrl: applications.webhookUrl,
        userId: applications.userId,
        createdAt: applications.createdAt,
        updatedAt: applications.updatedAt,
        balance: sql<string>`COALESCE(${applicationBalances.balance}, '0.00')`.as('balance'),
      })
      .from(applications)
      .leftJoin(applicationBalances, eq(applications.id, applicationBalances.applicationId))
      .where(and(eq(applications.id, applicationId), eq(applications.userId, ctx.user.id)))
      .limit(1)

    if (!result[0]) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '应用不存在或无权限访问',
      })
    }

    return next({
      ctx: {
        ...ctx,
        applicationId,
        applicationWithBalance: result[0],
      },
    })
  })
