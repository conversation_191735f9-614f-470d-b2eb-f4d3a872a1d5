import { initTRPC } from '@trpc/server'
import { ZodError } from 'zod'
import type { OpenApiMeta } from 'trpc-to-openapi'
import superjson from 'superjson'
import type { Context } from './context'
import { env } from './env'

export const t = initTRPC
  .meta<OpenApiMeta>()
  .context<Context>()
  .create({
    transformer: superjson,
    errorFormatter(opts) {
      const { shape, error } = opts
      if (error.code === 'INTERNAL_SERVER_ERROR' && env.NODE_ENV === 'production') {
        return { ...shape, message: 'Internal server error' }
      }
      return {
        ...shape,
        data: {
          ...shape.data,
          zodError: error.code === 'BAD_REQUEST' && error.cause instanceof ZodError ? error.cause.flatten() : null,
        },
      }
    },
  })

export const publicProcedure = t.procedure
export const router = t.router
