import type { CreateFastifyContextOptions } from '@trpc/server/adapters/fastify'
import { db } from './db'
import type { User } from './db/schema'

const createContext = async ({ req, res }: CreateFastifyContextOptions) => {
  const user: User | null = null
  return { req, res, db, user }
}

export type Context = Awaited<ReturnType<typeof createContext>> & {
  user: User | null
}

export default createContext
